function About() {
  return (
    <div className="relative w-full">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-900 to-black text-white py-32">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 animate-slide-up">
            About <span className="text-orange-500"><PERSON>yler</span> Construction
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto animate-fade-in-up">
            We are master builders dedicated to creating exceptional structures
            that stand the test of time and exceed expectations.
          </p>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="animate-slide-in-left">
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Construction Legacy</h2>
              <p className="text-lg text-gray-700 mb-6">
                Founded in 2009 with a vision to transform the construction industry, <PERSON>yler has grown from a
                small family business to one of the region's most trusted construction companies.
              </p>
              <p className="text-lg text-gray-700 mb-6">
                Our journey began with a simple belief: that every structure should be built with integrity,
                precision, and lasting quality. Today, we continue to build dreams and create legacies that
                stand the test of time.
              </p>
              <div className="grid grid-cols-3 gap-4 mt-8">
                <div className="text-center animate-count-up">
                  <div className="text-3xl font-bold text-orange-600">200+</div>
                  <div className="text-gray-600">Projects Completed</div>
                </div>
                <div className="text-center animate-count-up">
                  <div className="text-3xl font-bold text-blue-600">15+</div>
                  <div className="text-gray-600">Years Experience</div>
                </div>
                <div className="text-center animate-count-up">
                  <div className="text-3xl font-bold text-green-600">100%</div>
                  <div className="text-gray-600">Client Satisfaction</div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-orange-500 to-red-600 rounded-lg p-8 text-white animate-slide-in-right">
              <h3 className="text-2xl font-bold mb-4">Why Choose Taiyler?</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-yellow-300 mr-3">🏗️</span>
                  <div>
                    <strong>Master Craftsmen:</strong> Skilled builders with decades of construction expertise
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-300 mr-3">🔧</span>
                  <div>
                    <strong>Modern Equipment:</strong> We use the latest construction tools and technology
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-300 mr-3">🛡️</span>
                  <div>
                    <strong>Safety First:</strong> Uncompromising commitment to workplace safety
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-300 mr-3">⏰</span>
                  <div>
                    <strong>On-Time Delivery:</strong> We respect deadlines and deliver on schedule
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Meet Our Construction Team</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Our experienced team of construction professionals brings together decades of expertise and craftsmanship
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-left">
              <div className="w-24 h-24 bg-gradient-to-br from-orange-500 to-red-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-white text-2xl font-bold">RC</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Robert Chen</h3>
              <p className="text-orange-600 font-medium mb-3">CEO & Master Builder</p>
              <p className="text-gray-600 text-sm">
                Visionary leader with 20+ years of experience in construction management and project delivery.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-up">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-white text-2xl font-bold">SM</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Sarah Martinez</h3>
              <p className="text-blue-600 font-medium mb-3">Chief Engineer</p>
              <p className="text-gray-600 text-sm">
                Structural engineering expert specializing in innovative design and sustainable construction.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-right">
              <div className="w-24 h-24 bg-gradient-to-br from-green-500 to-teal-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-white text-2xl font-bold">DJ</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">David Johnson</h3>
              <p className="text-green-600 font-medium mb-3">Project Manager</p>
              <p className="text-gray-600 text-sm">
                Operations specialist focused on efficient project coordination and quality control.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-black text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl font-bold mb-6">Our Construction Values</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              These core principles guide every project we undertake and define our construction philosophy
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center animate-slide-in-left">
              <div className="w-16 h-16 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-3xl">🏗️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Quality Craftsmanship</h3>
              <p className="text-gray-300">
                We deliver superior construction quality that exceeds industry standards and client expectations.
              </p>
            </div>

            <div className="text-center animate-slide-in-up">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-3xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Integrity</h3>
              <p className="text-gray-300">
                We build trust through honest communication, transparent pricing, and ethical business practices.
              </p>
            </div>

            <div className="text-center animate-slide-in-down">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-3xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Safety Excellence</h3>
              <p className="text-gray-300">
                We maintain the highest safety standards to protect our workers and ensure secure job sites.
              </p>
            </div>

            <div className="text-center animate-slide-in-right">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-3xl">⏰</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Reliability</h3>
              <p className="text-gray-300">
                We honor our commitments and deliver projects on time and within budget, every time.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-6 animate-fade-in-up">Ready to Build Your Dream?</h2>
          <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto animate-fade-in-up">
            Let's discuss your construction project and turn your vision into reality with our expert craftsmanship.
          </p>
          <div className="space-x-4 animate-bounce-subtle">
            <button className="bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-300 shadow-lg">
              Get Free Quote
            </button>
            <button className="bg-transparent border-2 border-orange-600 text-orange-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-orange-600 hover:text-white transition-all duration-300">
              View Projects
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}

export default About;
