[{"B:\\Done projects\\Taiyler\\frontend\\src\\index.js": "1", "B:\\Done projects\\Taiyler\\frontend\\src\\App.js": "2", "B:\\Done projects\\Taiyler\\frontend\\src\\components\\Home.jsx": "3", "B:\\Done projects\\Taiyler\\frontend\\src\\components\\About.jsx": "4"}, {"size": 255, "mtime": 1753438310080, "results": "5", "hashOfConfig": "6"}, {"size": 2746, "mtime": 1753889927025, "results": "7", "hashOfConfig": "6"}, {"size": 20208, "mtime": 1753956582177, "results": "8", "hashOfConfig": "6"}, {"size": 11481, "mtime": 1753956661303, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "sx1xby", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "B:\\Done projects\\Taiyler\\frontend\\src\\index.js", [], [], "B:\\Done projects\\Taiyler\\frontend\\src\\App.js", [], [], "B:\\Done projects\\Taiyler\\frontend\\src\\components\\Home.jsx", ["22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32"], [], "B:\\Done projects\\Taiyler\\frontend\\src\\components\\About.jsx", [], [], {"ruleId": "33", "severity": 1, "message": "34", "line": 314, "column": 17, "nodeType": "35", "endLine": 314, "endColumn": 88}, {"ruleId": "33", "severity": 1, "message": "34", "line": 317, "column": 17, "nodeType": "35", "endLine": 317, "endColumn": 88}, {"ruleId": "33", "severity": 1, "message": "34", "line": 320, "column": 17, "nodeType": "35", "endLine": 320, "endColumn": 88}, {"ruleId": "33", "severity": 1, "message": "34", "line": 329, "column": 21, "nodeType": "35", "endLine": 329, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 330, "column": 21, "nodeType": "35", "endLine": 330, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 331, "column": 21, "nodeType": "35", "endLine": 331, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 332, "column": 21, "nodeType": "35", "endLine": 332, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 339, "column": 21, "nodeType": "35", "endLine": 339, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 340, "column": 21, "nodeType": "35", "endLine": 340, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 341, "column": 21, "nodeType": "35", "endLine": 341, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 342, "column": 21, "nodeType": "35", "endLine": 342, "endColumn": 73}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]