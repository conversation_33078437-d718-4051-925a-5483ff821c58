[{"B:\\Done projects\\Taiyler\\frontend\\src\\index.js": "1", "B:\\Done projects\\Taiyler\\frontend\\src\\App.js": "2", "B:\\Done projects\\Taiyler\\frontend\\src\\components\\Home.jsx": "3", "B:\\Done projects\\Taiyler\\frontend\\src\\components\\About.jsx": "4"}, {"size": 255, "mtime": 1753438310080, "results": "5", "hashOfConfig": "6"}, {"size": 1147, "mtime": 1753866094015, "results": "7", "hashOfConfig": "6"}, {"size": 11563, "mtime": 1753868839544, "results": "8", "hashOfConfig": "6"}, {"size": 9901, "mtime": 1753868893067, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "sx1xby", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "B:\\Done projects\\Taiyler\\frontend\\src\\index.js", [], [], "B:\\Done projects\\Taiyler\\frontend\\src\\App.js", [], [], "B:\\Done projects\\Taiyler\\frontend\\src\\components\\Home.jsx", ["22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32"], [], "B:\\Done projects\\Taiyler\\frontend\\src\\components\\About.jsx", [], [], {"ruleId": "33", "severity": 1, "message": "34", "line": 189, "column": 17, "nodeType": "35", "endLine": 189, "endColumn": 83}, {"ruleId": "33", "severity": 1, "message": "34", "line": 192, "column": 17, "nodeType": "35", "endLine": 192, "endColumn": 83}, {"ruleId": "33", "severity": 1, "message": "34", "line": 195, "column": 17, "nodeType": "35", "endLine": 195, "endColumn": 83}, {"ruleId": "33", "severity": 1, "message": "34", "line": 204, "column": 21, "nodeType": "35", "endLine": 204, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 205, "column": 21, "nodeType": "35", "endLine": 205, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 206, "column": 21, "nodeType": "35", "endLine": 206, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 207, "column": 21, "nodeType": "35", "endLine": 207, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 214, "column": 21, "nodeType": "35", "endLine": 214, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 215, "column": 21, "nodeType": "35", "endLine": 215, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 216, "column": 21, "nodeType": "35", "endLine": 216, "endColumn": 73}, {"ruleId": "33", "severity": 1, "message": "34", "line": 217, "column": 21, "nodeType": "35", "endLine": 217, "endColumn": 73}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]