{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Taiyler\\\\frontend\\\\src\\\\components\\\\About.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction About() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-gradient-to-br from-gray-900 to-black text-white py-32\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-5xl md:text-6xl font-bold mb-6 animate-slide-up\",\n          children: [\"About \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-orange-500\",\n            children: \"<PERSON><PERSON>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 8,\n            columnNumber: 19\n          }, this), \" Construction\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto animate-fade-in-up\",\n          children: \"We are master builders dedicated to creating exceptional structures that stand the test of time and exceed expectations.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-slide-in-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-4xl font-bold text-gray-900 mb-6\",\n              children: \"Our Construction Legacy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-700 mb-6\",\n              children: \"Founded in 2009 with a vision to transform the construction industry, Taiyler has grown from a small family business to one of the region's most trusted construction companies.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-700 mb-6\",\n              children: \"Our journey began with a simple belief: that every structure should be built with integrity, precision, and lasting quality. Today, we continue to build dreams and create legacies that stand the test of time.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-4 mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center animate-count-up\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-orange-600\",\n                  children: \"200+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-600\",\n                  children: \"Projects Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center animate-count-up\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-blue-600\",\n                  children: \"15+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-600\",\n                  children: \"Years Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center animate-count-up\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-green-600\",\n                  children: \"100%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-600\",\n                  children: \"Client Satisfaction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-orange-500 to-red-600 rounded-lg p-8 text-white animate-slide-in-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-4\",\n              children: \"Why Choose Taiyler?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-300 mr-3\",\n                  children: \"\\uD83C\\uDFD7\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Master Craftsmen:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 21\n                  }, this), \" Skilled builders with decades of construction expertise\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-300 mr-3\",\n                  children: \"\\uD83D\\uDD27\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Modern Equipment:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this), \" We use the latest construction tools and technology\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-300 mr-3\",\n                  children: \"\\uD83D\\uDEE1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Safety First:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 65,\n                    columnNumber: 21\n                  }, this), \" Uncompromising commitment to workplace safety\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-300 mr-3\",\n                  children: \"\\u23F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"On-Time Delivery:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 21\n                  }, this), \" We respect deadlines and deliver on schedule\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16 animate-fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-6\",\n            children: \"Meet Our Construction Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-700 max-w-3xl mx-auto\",\n            children: \"Our experienced team of construction professionals brings together decades of expertise and craftsmanship\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden border-4 border-orange-500\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"\\\\images\\\\WhatsApp Image 2025-07-30 at 15.11.22_e8cec0b5.jpg\",\n                alt: \"Robert Chen - CEO\",\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Uwimana Abdoulaziz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-orange-600 font-medium mb-3\",\n              children: \"CEO & Master Builder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Visionary leader with 20+ years of experience in construction management and project delivery.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden border-4 border-blue-500\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"\\\\images\\\\WhatsApp Image 2025-07-30 at 15.19.01_d1f40635.jpg\",\n                alt: \"Sarah Martinez - Chief Engineer\",\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Utukuzwe Ephrem\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-600 font-medium mb-3\",\n              children: \"Director of Finance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Senior executive who oversees a company's financial strategy, budgeting, reporting, and compliance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden border-4 border-green-500\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"images/team-manager.jpg\",\n                alt: \"David Johnson - Project Manager\",\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Habineza Jean Paul\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-600 font-medium mb-3\",\n              children: \"Operation Manager\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Operations specialist focused on efficient project coordination and quality control.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-gray-900 to-black text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16 animate-fade-in-up\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold mb-6\",\n            children: \"Our Construction Values\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n            children: \"These core principles guide every project we undertake and define our construction philosophy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center animate-slide-in-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl\",\n                children: \"\\uD83C\\uDFD7\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Quality Craftsmanship\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300\",\n              children: \"We deliver superior construction quality that exceeds industry standards and client expectations.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center animate-slide-in-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl\",\n                children: \"\\uD83E\\uDD1D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Integrity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300\",\n              children: \"We build trust through honest communication, transparent pricing, and ethical business practices.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center animate-slide-in-down\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl\",\n                children: \"\\uD83D\\uDEE1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Safety Excellence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300\",\n              children: \"We maintain the highest safety standards to protect our workers and ensure secure job sites.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center animate-slide-in-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl\",\n                children: \"\\u23F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Reliability\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300\",\n              children: \"We honor our commitments and deliver projects on time and within budget, every time.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-gray-900 mb-6 animate-fade-in-up\",\n          children: \"Ready to Build Your Dream?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-700 mb-8 max-w-2xl mx-auto animate-fade-in-up\",\n          children: \"Let's discuss your construction project and turn your vision into reality with our expert craftsmanship.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-4 animate-bounce-subtle\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-300 shadow-lg\",\n            children: \"Get Free Quote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-transparent border-2 border-orange-600 text-orange-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-orange-600 hover:text-white transition-all duration-300\",\n            children: \"View Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 5\n  }, this);\n}\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["About", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Taiyler/frontend/src/components/About.jsx"], "sourcesContent": ["function About() {\r\n  return (\r\n    <div className=\"relative w-full\">\r\n      {/* Hero Section */}\r\n      <section className=\"bg-gradient-to-br from-gray-900 to-black text-white py-32\">\r\n        <div className=\"container mx-auto px-4 text-center\">\r\n          <h1 className=\"text-5xl md:text-6xl font-bold mb-6 animate-slide-up\">\r\n            About <span className=\"text-orange-500\"><PERSON>yler</span> Construction\r\n          </h1>\r\n          <p className=\"text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto animate-fade-in-up\">\r\n            We are master builders dedicated to creating exceptional structures\r\n            that stand the test of time and exceed expectations.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Our Story Section */}\r\n      <section className=\"py-20 bg-white\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            <div className=\"animate-slide-in-left\">\r\n              <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">Our Construction Legacy</h2>\r\n              <p className=\"text-lg text-gray-700 mb-6\">\r\n                Founded in 2009 with a vision to transform the construction industry, <PERSON>yler has grown from a\r\n                small family business to one of the region's most trusted construction companies.\r\n              </p>\r\n              <p className=\"text-lg text-gray-700 mb-6\">\r\n                Our journey began with a simple belief: that every structure should be built with integrity,\r\n                precision, and lasting quality. Today, we continue to build dreams and create legacies that\r\n                stand the test of time.\r\n              </p>\r\n              <div className=\"grid grid-cols-3 gap-4 mt-8\">\r\n                <div className=\"text-center animate-count-up\">\r\n                  <div className=\"text-3xl font-bold text-orange-600\">200+</div>\r\n                  <div className=\"text-gray-600\">Projects Completed</div>\r\n                </div>\r\n                <div className=\"text-center animate-count-up\">\r\n                  <div className=\"text-3xl font-bold text-blue-600\">15+</div>\r\n                  <div className=\"text-gray-600\">Years Experience</div>\r\n                </div>\r\n                <div className=\"text-center animate-count-up\">\r\n                  <div className=\"text-3xl font-bold text-green-600\">100%</div>\r\n                  <div className=\"text-gray-600\">Client Satisfaction</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-gradient-to-br from-orange-500 to-red-600 rounded-lg p-8 text-white animate-slide-in-right\">\r\n              <h3 className=\"text-2xl font-bold mb-4\">Why Choose Taiyler?</h3>\r\n              <ul className=\"space-y-4\">\r\n                <li className=\"flex items-start\">\r\n                  <span className=\"text-yellow-300 mr-3\">🏗️</span>\r\n                  <div>\r\n                    <strong>Master Craftsmen:</strong> Skilled builders with decades of construction expertise\r\n                  </div>\r\n                </li>\r\n                <li className=\"flex items-start\">\r\n                  <span className=\"text-yellow-300 mr-3\">🔧</span>\r\n                  <div>\r\n                    <strong>Modern Equipment:</strong> We use the latest construction tools and technology\r\n                  </div>\r\n                </li>\r\n                <li className=\"flex items-start\">\r\n                  <span className=\"text-yellow-300 mr-3\">🛡️</span>\r\n                  <div>\r\n                    <strong>Safety First:</strong> Uncompromising commitment to workplace safety\r\n                  </div>\r\n                </li>\r\n                <li className=\"flex items-start\">\r\n                  <span className=\"text-yellow-300 mr-3\">⏰</span>\r\n                  <div>\r\n                    <strong>On-Time Delivery:</strong> We respect deadlines and deliver on schedule\r\n                  </div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Team Section */}\r\n      <section className=\"py-20 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"text-center mb-16 animate-fade-in-up\">\r\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">Meet Our Construction Team</h2>\r\n            <p className=\"text-xl text-gray-700 max-w-3xl mx-auto\">\r\n              Our experienced team of construction professionals brings together decades of expertise and craftsmanship\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            <div className=\"bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-left\">\r\n              <div className=\"w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden border-4 border-orange-500\">\r\n                <img\r\n                  src=\"\\images\\WhatsApp Image 2025-07-30 at 15.11.22_e8cec0b5.jpg\"\r\n                  alt=\"Robert Chen - CEO\"\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Uwimana Abdoulaziz</h3>\r\n              <p className=\"text-orange-600 font-medium mb-3\">CEO & Master Builder</p>\r\n              <p className=\"text-gray-600 text-sm\">\r\n                Visionary leader with 20+ years of experience in construction management and project delivery.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-up\">\r\n              <div className=\"w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden border-4 border-blue-500\">\r\n                <img\r\n                  src=\"\\images\\WhatsApp Image 2025-07-30 at 15.19.01_d1f40635.jpg\"\r\n                  alt=\"Sarah Martinez - Chief Engineer\"\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Utukuzwe Ephrem</h3>\r\n              <p className=\"text-blue-600 font-medium mb-3\">Director of Finance</p>\r\n              <p className=\"text-gray-600 text-sm\">\r\n                Senior executive who oversees a company's financial strategy, budgeting, reporting, and compliance.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-right\">\r\n              <div className=\"w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden border-4 border-green-500\">\r\n                <img\r\n                  src=\"images/team-manager.jpg\"\r\n                  alt=\"David Johnson - Project Manager\"\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Habineza Jean Paul</h3>\r\n              <p className=\"text-green-600 font-medium mb-3\">Operation Manager</p>\r\n              <p className=\"text-gray-600 text-sm\">\r\n                Operations specialist focused on efficient project coordination and quality control.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Values Section */}\r\n      <section className=\"py-20 bg-gradient-to-br from-gray-900 to-black text-white\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"text-center mb-16 animate-fade-in-up\">\r\n            <h2 className=\"text-4xl font-bold mb-6\">Our Construction Values</h2>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n              These core principles guide every project we undertake and define our construction philosophy\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            <div className=\"text-center animate-slide-in-left\">\r\n              <div className=\"w-16 h-16 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\">\r\n                <span className=\"text-3xl\">🏗️</span>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3\">Quality Craftsmanship</h3>\r\n              <p className=\"text-gray-300\">\r\n                We deliver superior construction quality that exceeds industry standards and client expectations.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"text-center animate-slide-in-up\">\r\n              <div className=\"w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\">\r\n                <span className=\"text-3xl\">🤝</span>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3\">Integrity</h3>\r\n              <p className=\"text-gray-300\">\r\n                We build trust through honest communication, transparent pricing, and ethical business practices.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"text-center animate-slide-in-down\">\r\n              <div className=\"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\">\r\n                <span className=\"text-3xl\">🛡️</span>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3\">Safety Excellence</h3>\r\n              <p className=\"text-gray-300\">\r\n                We maintain the highest safety standards to protect our workers and ensure secure job sites.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"text-center animate-slide-in-right\">\r\n              <div className=\"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow\">\r\n                <span className=\"text-3xl\">⏰</span>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold mb-3\">Reliability</h3>\r\n              <p className=\"text-gray-300\">\r\n                We honor our commitments and deliver projects on time and within budget, every time.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-20 bg-white\">\r\n        <div className=\"container mx-auto px-4 text-center\">\r\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-6 animate-fade-in-up\">Ready to Build Your Dream?</h2>\r\n          <p className=\"text-xl text-gray-700 mb-8 max-w-2xl mx-auto animate-fade-in-up\">\r\n            Let's discuss your construction project and turn your vision into reality with our expert craftsmanship.\r\n          </p>\r\n          <div className=\"space-x-4 animate-bounce-subtle\">\r\n            <button className=\"bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-300 shadow-lg\">\r\n              Get Free Quote\r\n            </button>\r\n            <button className=\"bg-transparent border-2 border-orange-600 text-orange-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-orange-600 hover:text-white transition-all duration-300\">\r\n              View Projects\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default About;\r\n"], "mappings": ";;AAAA,SAASA,KAAKA,CAAA,EAAG;EACf,oBACEC,OAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE9BF,OAAA;MAASC,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eAC5EF,OAAA;QAAKC,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDF,OAAA;UAAIC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,GAAC,QAC7D,eAAAF,OAAA;YAAMC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,iBACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLN,OAAA;UAAGC,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAGtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCF,OAAA;QAAKC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCF,OAAA;UAAKC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACtDF,OAAA;YAAKC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCF,OAAA;cAAIC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFN,OAAA;cAAGC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAG1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJN,OAAA;cAAGC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAI1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJN,OAAA;cAAKC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CF,OAAA;gBAAKC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CF,OAAA;kBAAKC,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9DN,OAAA;kBAAKC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CF,OAAA;kBAAKC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3DN,OAAA;kBAAKC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CF,OAAA;kBAAKC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7DN,OAAA;kBAAKC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNN,OAAA;YAAKC,SAAS,EAAC,+FAA+F;YAAAC,QAAA,gBAC5GF,OAAA;cAAIC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEN,OAAA;cAAIC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBF,OAAA;gBAAIC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BF,OAAA;kBAAMC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAAE,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,4DACpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLN,OAAA;gBAAIC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BF,OAAA;kBAAMC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAAE,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,wDACpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLN,OAAA;gBAAIC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BF,OAAA;kBAAMC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAAE,QAAA,EAAQ;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,kDAChC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLN,OAAA;gBAAIC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BF,OAAA;kBAAMC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAAE,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,iDACpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCF,OAAA;QAAKC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCF,OAAA;UAAKC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnDF,OAAA;YAAIC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFN,OAAA;YAAGC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENN,OAAA;UAAKC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvDF,OAAA;YAAKC,SAAS,EAAC,iIAAiI;YAAAC,QAAA,gBAC9IF,OAAA;cAAKC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7FF,OAAA;gBACEO,GAAG,EAAC,8DAA4D;gBAChEC,GAAG,EAAC,mBAAmB;gBACvBP,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFN,OAAA;cAAGC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxEN,OAAA;cAAGC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,+HAA+H;YAAAC,QAAA,gBAC5IF,OAAA;cAAKC,SAAS,EAAC,8EAA8E;cAAAC,QAAA,eAC3FF,OAAA;gBACEO,GAAG,EAAC,8DAA4D;gBAChEC,GAAG,EAAC,iCAAiC;gBACrCP,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EN,OAAA;cAAGC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEN,OAAA;cAAGC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,kIAAkI;YAAAC,QAAA,gBAC/IF,OAAA;cAAKC,SAAS,EAAC,+EAA+E;cAAAC,QAAA,eAC5FF,OAAA;gBACEO,GAAG,EAAC,yBAAyB;gBAC7BC,GAAG,EAAC,iCAAiC;gBACrCP,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFN,OAAA;cAAGC,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEN,OAAA;cAAGC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eAC5EF,OAAA;QAAKC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCF,OAAA;UAAKC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnDF,OAAA;YAAIC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEN,OAAA;YAAGC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENN,OAAA;UAAKC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvDF,OAAA;YAAKC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDF,OAAA;cAAKC,SAAS,EAAC,0GAA0G;cAAAC,QAAA,eACvHF,OAAA;gBAAMC,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEN,OAAA;cAAGC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CF,OAAA;cAAKC,SAAS,EAAC,wGAAwG;cAAAC,QAAA,eACrHF,OAAA;gBAAMC,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDN,OAAA;cAAGC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDF,OAAA;cAAKC,SAAS,EAAC,yGAAyG;cAAAC,QAAA,eACtHF,OAAA;gBAAMC,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEN,OAAA;cAAGC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjDF,OAAA;cAAKC,SAAS,EAAC,uGAAuG;cAAAC,QAAA,eACpHF,OAAA;gBAAMC,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNN,OAAA;cAAIC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DN,OAAA;cAAGC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCF,OAAA;QAAKC,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDF,OAAA;UAAIC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxGN,OAAA;UAAGC,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJN,OAAA;UAAKC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CF,OAAA;YAAQC,SAAS,EAAC,gLAAgL;YAAAC,QAAA,EAAC;UAEnM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTN,OAAA;YAAQC,SAAS,EAAC,uKAAuK;YAAAC,QAAA,EAAC;UAE1L;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACG,EAAA,GAnNQV,KAAK;AAqNd,eAAeA,KAAK;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}