function Home() {
  return (
    <div className="relative w-full">
      {/* Hero Section with Video Background */}
      <section className="relative w-full h-screen overflow-hidden">
        {/* Background video that extends behind navbar */}
       <img
  src=""
  alt="Hero"
  className="w-full h-auto object-cover"
/>


        {/* Hero content - completely transparent */}
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white px-4 animate-fade-in">
          <h1 className="text-6xl md:text-7xl font-bold mb-6 drop-shadow-2xl text-center animate-slide-up">
            Taiyler Construction
          </h1>
          <p className="text-xl md:text-2xl drop-shadow-2xl text-center max-w-3xl mb-8 animate-slide-up-delay">
            Building Dreams, Creating Legacies - Your Trusted Construction Partner
          </p>
          <button className="bg-orange-600/80 backdrop-blur-sm text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-700/90 transition-all duration-300 drop-shadow-xl animate-bounce-subtle">
            Get Free Quote
          </button>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-black text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Why Choose BuildCraft?</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              With over 15 years of experience, we deliver exceptional construction projects
              that stand the test of time.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6 animate-slide-in-left">
              <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-2xl">🏗️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Expert Craftsmanship</h3>
              <p className="text-gray-300">
                Our skilled craftsmen bring decades of experience to every project, ensuring quality that exceeds expectations.
              </p>
            </div>

            <div className="text-center p-6 animate-slide-in-up">
              <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-2xl">⏰</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">On-Time Delivery</h3>
              <p className="text-gray-300">
                We understand the importance of deadlines and consistently deliver projects on schedule and within budget.
              </p>
            </div>

            <div className="text-center p-6 animate-slide-in-right">
              <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-2xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Safety First</h3>
              <p className="text-gray-300">
                Safety is our top priority. We maintain the highest safety standards on every job site.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Services Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 to-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Our Construction Services</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              From residential homes to commercial complexes, we deliver comprehensive construction solutions
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-left">
              <div className="w-12 h-12 bg-orange-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🏠</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Residential Construction</h3>
              <p className="text-gray-300 mb-4">
                Custom homes, renovations, and residential developments built to your specifications.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Custom Home Building</li>
                <li>• Home Renovations</li>
                <li>• Kitchen & Bath Remodeling</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-up">
              <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🏢</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Commercial Construction</h3>
              <p className="text-gray-300 mb-4">
                Office buildings, retail spaces, and industrial facilities designed for success.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Office Buildings</li>
                <li>• Retail Spaces</li>
                <li>• Warehouses</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-right">
              <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🔨</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Renovation & Remodeling</h3>
              <p className="text-gray-300 mb-4">
                Transform existing spaces with our expert renovation and remodeling services.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Interior Renovations</li>
                <li>• Exterior Upgrades</li>
                <li>• Structural Modifications</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-left">
              <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🛣️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Infrastructure</h3>
              <p className="text-gray-300 mb-4">
                Roads, bridges, and civil engineering projects that build communities.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Road Construction</li>
                <li>• Bridge Building</li>
                <li>• Utility Installation</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-up">
              <div className="w-12 h-12 bg-red-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📐</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Design & Planning</h3>
              <p className="text-gray-300 mb-4">
                Comprehensive architectural design and project planning services.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Architectural Design</li>
                <li>• Project Planning</li>
                <li>• Permit Assistance</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-right">
              <div className="w-12 h-12 bg-yellow-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🔧</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Maintenance & Repair</h3>
              <p className="text-gray-300 mb-4">
                Ongoing maintenance and repair services to keep your property in top condition.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Preventive Maintenance</li>
                <li>• Emergency Repairs</li>
                <li>• Property Inspections</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Our Projects Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Featured Projects</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Take a look at some of our recent construction projects that showcase our expertise and quality
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-left">
              <div className="h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                <span className="text-white text-6xl">🏢</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Downtown Office Complex</h3>
                <p className="text-gray-600 mb-4">
                  A modern 12-story office building featuring sustainable design and state-of-the-art facilities.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>📍 Downtown District</span>
                  <span>⏱️ 18 months</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-up">
              <div className="h-48 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                <span className="text-white text-6xl">🏠</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Luxury Residential Estate</h3>
                <p className="text-gray-600 mb-4">
                  Custom-built luxury homes with premium finishes and smart home technology integration.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>📍 Hillside Heights</span>
                  <span>⏱️ 14 months</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-right">
              <div className="h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                <span className="text-white text-6xl">🏪</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Retail Shopping Center</h3>
                <p className="text-gray-600 mb-4">
                  Multi-tenant retail complex with modern amenities and ample parking facilities.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>📍 Commerce Plaza</span>
                  <span>⏱️ 12 months</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-left">
              <div className="h-48 bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                <span className="text-white text-6xl">🏥</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Medical Center Expansion</h3>
                <p className="text-gray-600 mb-4">
                  State-of-the-art medical facility expansion with specialized treatment rooms and equipment.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>📍 Medical District</span>
                  <span>⏱️ 16 months</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-up">
              <div className="h-48 bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                <span className="text-white text-6xl">🏫</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Educational Campus</h3>
                <p className="text-gray-600 mb-4">
                  Modern educational facility with advanced learning spaces and recreational areas.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>📍 University District</span>
                  <span>⏱️ 20 months</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-right">
              <div className="h-48 bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center">
                <span className="text-white text-6xl">🏭</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Industrial Warehouse</h3>
                <p className="text-gray-600 mb-4">
                  Large-scale warehouse facility with automated systems and efficient logistics design.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>📍 Industrial Park</span>
                  <span>⏱️ 10 months</span>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <button className="bg-orange-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-700 transition-all duration-300 shadow-lg animate-bounce-subtle">
              View All Projects
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div className="animate-fade-in-up">
              <h3 className="text-xl font-bold mb-4 text-orange-500">BuildCraft Construction</h3>
              <p className="text-gray-400 mb-4">
                Building dreams and creating lasting legacies with over 15 years of construction excellence.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-orange-500 transition">
                  <span className="text-xl">📧</span>
                </a>
                <a href="#" className="text-gray-400 hover:text-orange-500 transition">
                  <span className="text-xl">📱</span>
                </a>
                <a href="#" className="text-gray-400 hover:text-orange-500 transition">
                  <span className="text-xl">🌐</span>
                </a>
              </div>
            </div>

            <div className="animate-fade-in-up">
              <h4 className="text-lg font-semibold mb-4 text-orange-500">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition">Residential Construction</a></li>
                <li><a href="#" className="hover:text-white transition">Commercial Buildings</a></li>
                <li><a href="#" className="hover:text-white transition">Renovations</a></li>
                <li><a href="#" className="hover:text-white transition">Infrastructure</a></li>
              </ul>
            </div>

            <div className="animate-fade-in-up">
              <h4 className="text-lg font-semibold mb-4 text-orange-500">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition">About Us</a></li>
                <li><a href="#" className="hover:text-white transition">Our Projects</a></li>
                <li><a href="#" className="hover:text-white transition">Careers</a></li>
                <li><a href="#" className="hover:text-white transition">Contact</a></li>
              </ul>
            </div>

            <div className="animate-fade-in-up">
              <h4 className="text-lg font-semibold mb-4 text-orange-500">Contact Info</h4>
              <div className="space-y-2 text-gray-400">
                <p>📍 456 Construction Ave</p>
                <p>Builder City, BC 12345</p>
                <p>📞 (555) BUILD-NOW</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 BuildCraft Construction. All rights reserved. | Licensed & Insured</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default Home;
