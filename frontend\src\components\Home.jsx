function Home() {
  return (
    <div className="relative w-full">
      {/* Hero Section with Video Background */}
      <section className="relative w-full h-screen overflow-hidden">
        {/* Background image that extends behind navbar */}
        <img
          src="images/679bf98340b145e2aec4749c27bf883c.webp"
          alt="Construction Hero"
          className="absolute top-0 left-0 w-full h-full object-cover z-0"
        />

        {/* Hero content - completely transparent overlay */}
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center text-white px-4 animate-fade-in">
          <h1 className="text-6xl md:text-7xl font-bold mb-6 drop-shadow-2xl text-center animate-slide-up">
            Taiyler Construction
          </h1>
          <p className="text-xl md:text-2xl drop-shadow-2xl text-center max-w-3xl mb-8 animate-slide-up-delay">
            Building Dreams, Creating Legacies - Your Trusted Construction Partner
          </p>
          <button className="bg-orange-600/80 backdrop-blur-sm text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-700/90 transition-all duration-300 drop-shadow-xl animate-bounce-subtle">
            Get Free Quote
          </button>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-black text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Why Choose Taiyler?</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              With over 15 years of experience, we deliver exceptional construction projects
              that stand the test of time.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6 animate-slide-in-left">
              <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-2xl">🏗️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Expert Craftsmanship</h3>
              <p className="text-gray-300">
                Our skilled craftsmen bring decades of experience to every project, ensuring quality that exceeds expectations.
              </p>
            </div>

            <div className="text-center p-6 animate-slide-in-up">
              <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-2xl">⏰</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">On-Time Delivery</h3>
              <p className="text-gray-300">
                We understand the importance of deadlines and consistently deliver projects on schedule and within budget.
              </p>
            </div>

            <div className="text-center p-6 animate-slide-in-right">
              <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
                <span className="text-2xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Safety First</h3>
              <p className="text-gray-300">
                Safety is our top priority. We maintain the highest safety standards on every job site.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Services Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 to-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Our Construction Services</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              From residential homes to commercial complexes, we deliver comprehensive construction solutions
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-left">
              <div className="w-12 h-12 bg-orange-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🏠</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Residential Construction</h3>
              <p className="text-gray-300 mb-4">
                Custom homes, renovations, and residential developments built to your specifications.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Custom Home Building</li>
                <li>• Home Renovations</li>
                <li>• Kitchen & Bath Remodeling</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-up">
              <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🏢</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Commercial Construction</h3>
              <p className="text-gray-300 mb-4">
                Office buildings, retail spaces, and industrial facilities designed for success.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Office Buildings</li>
                <li>• Retail Spaces</li>
                <li>• Warehouses</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-right">
              <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🔨</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Renovation & Remodeling</h3>
              <p className="text-gray-300 mb-4">
                Transform existing spaces with our expert renovation and remodeling services.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Interior Renovations</li>
                <li>• Exterior Upgrades</li>
                <li>• Structural Modifications</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-left">
              <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🛣️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Infrastructure</h3>
              <p className="text-gray-300 mb-4">
                Roads, bridges, and civil engineering projects that build communities.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Road Construction</li>
                <li>• Bridge Building</li>
                <li>• Utility Installation</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-up">
              <div className="w-12 h-12 bg-red-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📐</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Design & Planning</h3>
              <p className="text-gray-300 mb-4">
                Comprehensive architectural design and project planning services.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Architectural Design</li>
                <li>• Project Planning</li>
                <li>• Permit Assistance</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 hover:scale-105 transition-all duration-300 animate-slide-in-right">
              <div className="w-12 h-12 bg-yellow-600/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🔧</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Maintenance & Repair</h3>
              <p className="text-gray-300 mb-4">
                Ongoing maintenance and repair services to keep your property in top condition.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Preventive Maintenance</li>
                <li>• Emergency Repairs</li>
                <li>• Property Inspections</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Our Projects Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Featured Projects</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Take a look at some of our recent construction projects that showcase our expertise and quality
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="relative group rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-left h-80">
              <img
                src="\images\WhatsApp Image 2025-07-30 at 15.21.22_76658395.jpg"
                alt="Downtown Office Complex"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                <h3 className="text-xl font-semibold text-white mb-2">Downtown Duplex</h3>
                <p className="text-gray-200 mb-4 text-sm">
                  A modern 2-in-one family building featuring sustainable design and state-of-the-art facilities.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-300">
                  <span>📍 Downtown District</span>
                  <span>⏱️ 18 months</span>
                </div>
              </div>
            </div>

            <div className="relative group rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-up h-80">
              <img
                src="\images\WhatsApp Image 2025-07-27 at 22.43.49_1ba3ccee.jpg"
                alt="Luxury Residential Estate"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                <h3 className="text-xl font-semibold text-white mb-2">Luxury Residential Estate</h3>
                <p className="text-gray-200 mb-4 text-sm">
                  Custom-built luxury homes with premium finishes and smart home technology integration.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-300">
                  <span>📍 Hillside Heights</span>
                  <span>⏱️ 14 months</span>
                </div>
              </div>
            </div>

            <div className="relative group rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-right h-80">
              <img
                src="\images\norsken.webp"
                alt="Retail Shopping Center"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                <h3 className="text-xl font-semibold text-white mb-2">Norskene Rwanda</h3>
                <p className="text-gray-200 mb-4 text-sm">
                  Multi-task  complex with modern amenities and ample parking facilities.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-300">
                  <span>📍 Commerce Plaza</span>
                  <span>⏱️ 12 months</span>
                </div>
              </div>
            </div>

            <div className="relative group rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-left h-80">
              <img
                src="\images\WhatsApp Image 2025-07-30 at 16.41.54_7e062ab8.jpg"
                alt="Medical Center Expansion"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                <h3 className="text-xl font-semibold text-white mb-2">Frontier Diagnostic Center</h3>
                <p className="text-gray-200 mb-4 text-sm">
                  State-of-the-art medical facility expansion with specialized treatment rooms and equipment.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-300">
                  <span>📍 Medical District</span>
                  <span>⏱️ 16 months</span>
                </div>
              </div>
            </div>

            <div className="relative group rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-up h-80">
              <img
                src="\images\WhatsApp Image 2025-07-30 at 15.21.22_e4eff9a9.jpg"
                alt="Educational Campus"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                <h3 className="text-xl font-semibold text-white mb-2">Modern Appartments</h3>
                <p className="text-gray-200 mb-4 text-sm">
                  Modern appartment facility with advanced amenities and parking areas.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-300">
                  <span>📍 kicukiro District</span>
                  <span>⏱️ 12 months</span>
                </div>
              </div>
            </div>

            <div className="relative group rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 animate-slide-in-right h-80">
              <img
                src="\images\post-norrsken_eastafrica-nov-28-2021.jpeg"
                alt="Industrial Warehouse"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                <h3 className="text-xl font-semibold text-white mb-2">Industrial Warehouse</h3>
                <p className="text-gray-200 mb-4 text-sm">
                  Large-scale warehouse facility with automated systems and efficient logistics design.
                </p>
                <div className="flex justify-between items-center text-sm text-gray-300">
                  <span>📍 Industrial Park</span>
                  <span>⏱️ 10 months</span>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <button className="bg-orange-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-700 transition-all duration-300 shadow-lg animate-bounce-subtle">
              View All Projects
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div className="animate-fade-in-up">
              <h3 className="text-xl font-bold mb-4 text-orange-500">Taiyler Construction</h3>
              <p className="text-gray-400 mb-4">
                Building dreams and creating lasting legacies with over 15 years of construction excellence.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-orange-500 transition">
                  <span className="text-xl">📧</span>
                </a>
                <a href="#" className="text-gray-400 hover:text-orange-500 transition">
                  <span className="text-xl">📱</span>
                </a>
                <a href="#" className="text-gray-400 hover:text-orange-500 transition">
                  <span className="text-xl">🌐</span>
                </a>
              </div>
            </div>

            <div className="animate-fade-in-up">
              <h4 className="text-lg font-semibold mb-4 text-orange-500">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition">Residential Construction</a></li>
                <li><a href="#" className="hover:text-white transition">Commercial Buildings</a></li>
                <li><a href="#" className="hover:text-white transition">Renovations</a></li>
                <li><a href="#" className="hover:text-white transition">Infrastructure</a></li>
              </ul>
            </div>

            <div className="animate-fade-in-up">
              <h4 className="text-lg font-semibold mb-4 text-orange-500">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition">About Us</a></li>
                <li><a href="#" className="hover:text-white transition">Our Projects</a></li>
                <li><a href="#" className="hover:text-white transition">Careers</a></li>
                <li><a href="#" className="hover:text-white transition">Contact</a></li>
              </ul>
            </div>

            <div className="animate-fade-in-up">
              <h4 className="text-lg font-semibold mb-4 text-orange-500">Contact Info</h4>
              <div className="space-y-2 text-gray-400">
                <p>📍 456 Construction Ave</p>
                <p>Builder City, BC 12345</p>
                <p>📞 (555) BUILD-NOW</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Taiyler Construction. All rights reserved. | Licensed & Insured</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default Home;
