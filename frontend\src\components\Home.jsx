function Home() {
  return (
    <div className="relative w-full">
      {/* Hero Section with Video Background */}
      <section className="relative w-full h-screen overflow-hidden">
        {/* Background video that extends behind navbar */}
        <video
          className="absolute top-0 left-0 w-full h-full object-cover z-0"
          src="videos/Untitled video - Made with Clipchamp.mp4"
          autoPlay
          muted
          loop
          playsInline
        />

        {/* Hero content - completely transparent */}
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white px-4">
          <h1 className="text-6xl md:text-7xl font-bold mb-6 drop-shadow-2xl text-center">
            Welcome to MySite
          </h1>
          <p className="text-xl md:text-2xl drop-shadow-2xl text-center max-w-3xl mb-8">
            Transforming ideas into exceptional digital experiences
          </p>
          <button className="bg-white/20 backdrop-blur-sm text-white px-8 py-3 rounded-full text-lg font-semibold hover:bg-white/30 transition-all duration-300 drop-shadow-xl">
            Get Started
          </button>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-black text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">About Us</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We are a team of passionate professionals dedicated to delivering innovative solutions
              that drive success for our clients.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Our Mission</h3>
              <p className="text-gray-300">
                To empower businesses with cutting-edge technology solutions that drive growth and innovation.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">👁️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Our Vision</h3>
              <p className="text-gray-300">
                To be the leading provider of transformative digital experiences that shape the future.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Our Values</h3>
              <p className="text-gray-300">
                Innovation, integrity, and excellence in everything we do, with a focus on client success.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Services Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 to-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Our Services</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We offer comprehensive solutions tailored to meet your unique business needs
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">💻</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Web Development</h3>
              <p className="text-gray-300 mb-4">
                Custom web applications built with modern technologies and best practices.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• React & Next.js</li>
                <li>• Node.js & Express</li>
                <li>• Database Integration</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📱</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Mobile Apps</h3>
              <p className="text-gray-300 mb-4">
                Native and cross-platform mobile applications for iOS and Android.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• React Native</li>
                <li>• Flutter</li>
                <li>• Native Development</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🎨</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">UI/UX Design</h3>
              <p className="text-gray-300 mb-4">
                Beautiful, intuitive designs that enhance user experience and engagement.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• User Research</li>
                <li>• Wireframing</li>
                <li>• Prototyping</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">☁️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Cloud Solutions</h3>
              <p className="text-gray-300 mb-4">
                Scalable cloud infrastructure and deployment solutions for your applications.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• AWS & Azure</li>
                <li>• DevOps</li>
                <li>• CI/CD Pipelines</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🔒</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Security</h3>
              <p className="text-gray-300 mb-4">
                Comprehensive security solutions to protect your digital assets and data.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Security Audits</li>
                <li>• Penetration Testing</li>
                <li>• Compliance</li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-12 h-12 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">📊</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Analytics</h3>
              <p className="text-gray-300 mb-4">
                Data-driven insights and analytics to help you make informed business decisions.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Data Visualization</li>
                <li>• Business Intelligence</li>
                <li>• Performance Metrics</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-bold mb-4">MySite</h3>
              <p className="text-gray-400 mb-4">
                Transforming ideas into exceptional digital experiences with innovation and excellence.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white transition">
                  <span className="text-xl">📧</span>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition">
                  <span className="text-xl">📱</span>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition">
                  <span className="text-xl">🌐</span>
                </a>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition">Web Development</a></li>
                <li><a href="#" className="hover:text-white transition">Mobile Apps</a></li>
                <li><a href="#" className="hover:text-white transition">UI/UX Design</a></li>
                <li><a href="#" className="hover:text-white transition">Cloud Solutions</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition">About Us</a></li>
                <li><a href="#" className="hover:text-white transition">Our Team</a></li>
                <li><a href="#" className="hover:text-white transition">Careers</a></li>
                <li><a href="#" className="hover:text-white transition">Contact</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <div className="space-y-2 text-gray-400">
                <p>📍 123 Business Street</p>
                <p>City, State 12345</p>
                <p>📞 (555) 123-4567</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 MySite. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default Home;
