{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Taiyler\\\\frontend\\\\src\\\\components\\\\Home.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative w-full h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n      className: \"absolute top-0 left-0 w-full h-full object-cover z-0\",\n      src: \"videos/Untitled video - Made with Clipchamp.mp4\",\n      autoPlay: true,\n      muted: true,\n      loop: true,\n      playsInline: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex flex-col items-center justify-center h-full bg-black/30 text-white px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-5xl font-bold mb-4 drop-shadow-lg\",\n        children: \"Welcome to My Site\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xl drop-shadow-lg\",\n        children: \"This is a homepage with a background video.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 5\n  }, this);\n}\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["Home", "_jsxDEV", "className", "children", "src", "autoPlay", "muted", "loop", "playsInline", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Taiyler/frontend/src/components/Home.jsx"], "sourcesContent": ["function Home() {\r\n  return (\r\n    <div className=\"relative w-full h-screen overflow-hidden\">\r\n      {/* Background video that extends behind navbar */}\r\n      <video\r\n        className=\"absolute top-0 left-0 w-full h-full object-cover z-0\"\r\n        src=\"videos/Untitled video - Made with Clipchamp.mp4\"\r\n        autoPlay\r\n        muted\r\n        loop\r\n        playsInline\r\n      />\r\n\r\n      {/* Overlay content */}\r\n      <div className=\"relative z-10 flex flex-col items-center justify-center h-full bg-black/30 text-white px-4\">\r\n        <h1 className=\"text-5xl font-bold mb-4 drop-shadow-lg\">Welcome to My Site</h1>\r\n        <p className=\"text-xl drop-shadow-lg\">This is a homepage with a background video.</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,SAASA,IAAIA,CAAA,EAAG;EACd,oBACEC,OAAA;IAAKC,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAEvDF,OAAA;MACEC,SAAS,EAAC,sDAAsD;MAChEE,GAAG,EAAC,iDAAiD;MACrDC,QAAQ;MACRC,KAAK;MACLC,IAAI;MACJC,WAAW;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAGFX,OAAA;MAAKC,SAAS,EAAC,4FAA4F;MAAAC,QAAA,gBACzGF,OAAA;QAAIC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAkB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9EX,OAAA;QAAGC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAA2C;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GApBQb,IAAI;AAsBb,eAAeA,IAAI;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}