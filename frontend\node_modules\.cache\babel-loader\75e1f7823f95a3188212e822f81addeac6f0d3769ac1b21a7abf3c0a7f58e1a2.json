{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Taiyler\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport Home from './components/Home';\nimport About from './components/About';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"bg-transparent p-4 shadow-md backdrop-blur-md fixed w-full z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-white text-xl font-bold\",\n          children: \"MySite\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-white hover:text-gray-200 transition\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"text-white hover:text-gray-200 transition\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-20\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/about\",\n          element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Link", "Home", "About", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "path", "element", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Taiyler/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport Home from './components/Home';\nimport About from './components/About';\nimport './index.css';\n\nfunction App() {\n  return (\n    <Router>\n      {/* Transparent Navbar */}\n      <nav className=\"bg-transparent p-4 shadow-md backdrop-blur-md fixed w-full z-50\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <div className=\"text-white text-xl font-bold\">MySite</div>\n          <div className=\"space-x-4\">\n            <Link to=\"/\" className=\"text-white hover:text-gray-200 transition\">\n              Home\n            </Link>\n            <Link to=\"/about\" className=\"text-white hover:text-gray-200 transition\">\n              About\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Push content down so it's not hidden behind navbar */}\n      <div className=\"pt-20\">\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/about\" element={<About />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,MAAM;IAAAS,QAAA,gBAELF,OAAA;MAAKG,SAAS,EAAC,iEAAiE;MAAAD,QAAA,eAC9EF,OAAA;QAAKG,SAAS,EAAC,qDAAqD;QAAAD,QAAA,gBAClEF,OAAA;UAAKG,SAAS,EAAC,8BAA8B;UAAAD,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1DP,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA,CAACJ,IAAI;YAACY,EAAE,EAAC,GAAG;YAACL,SAAS,EAAC,2CAA2C;YAAAD,QAAA,EAAC;UAEnE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPP,OAAA,CAACJ,IAAI;YAACY,EAAE,EAAC,QAAQ;YAACL,SAAS,EAAC,2CAA2C;YAAAD,QAAA,EAAC;UAExE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKG,SAAS,EAAC,OAAO;MAAAD,QAAA,eACpBF,OAAA,CAACL,MAAM;QAAAO,QAAA,gBACLF,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEV,OAAA,CAACH,IAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCP,OAAA,CAACN,KAAK;UAACe,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEV,OAAA,CAACF,KAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACI,EAAA,GA3BQV,GAAG;AA6BZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}