// App.js
import React from 'react';
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';
import Home from './components/Home';
import About from './components/About';
import './index.css';

function App() {
  return (
    <Router>
      {/* Fully Transparent Navbar */}
      <nav className="bg-transparent p-4 fixed w-full z-50">
        <div className="container mx-auto flex justify-between items-center">
          <div className="text-white text-xl font-bold drop-shadow-lg">MySite</div>
          <div className="space-x-4">
            <Link to="/" className="text-white hover:text-gray-200 transition drop-shadow-lg">
              Home
            </Link>
            <Link to="/about" className="text-white hover:text-gray-200 transition drop-shadow-lg">
              About
            </Link>
          </div>
        </div>
      </nav>

      {/* No padding needed as navbar is fully transparent */}
      <div>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
