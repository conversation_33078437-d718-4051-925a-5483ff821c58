// App.js
import React from 'react';
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';
import Home from './components/Home';
import About from './components/About';
import './index.css';

function App() {
  return (
    <Router>
      {/* Fully Transparent Navbar */}
      <nav className="bg-transparent p-4 fixed w-full z-50">
        <div className="container mx-auto flex justify-between items-center">
          {/* Logo and Brand Section */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="absolute inset-0 bg-white/10 rounded-full blur-sm group-hover:bg-white/20 transition-all duration-300"></div>
              <img
                src="images/logo.png"
                alt="Taiyler Construction Logo"
                className="relative h-12 w-12 rounded-full border-2 border-white/20 group-hover:border-orange-400/60 transition-all duration-300 drop-shadow-xl"
              />
            </div>
            <div className="text-white">
              <div className="text-xl font-bold drop-shadow-lg group-hover:text-orange-200 transition-colors duration-300">
                TAIYLER
              </div>
              <div className="text-sm font-medium text-gray-200 drop-shadow-lg -mt-1">
                Construction
              </div>
            </div>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center space-x-8">
            <Link to="/" className="text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium text-lg relative group">
              Home
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-400 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link to="/about" className="text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium text-lg relative group">
              About
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-400 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <button className="bg-orange-600/80 backdrop-blur-sm text-white px-6 py-2.5 rounded-lg hover:bg-orange-700/90 hover:scale-105 transition-all duration-300 drop-shadow-lg font-semibold border border-orange-500/30">
              Get Quote
            </button>
          </div>
        </div>
      </nav>

      {/* No padding needed as navbar is fully transparent */}
      <div>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
