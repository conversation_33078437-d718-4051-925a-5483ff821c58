// App.js
import React from 'react';
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';
import Home from './components/Home';
import About from './components/About';
import './index.css';

function App() {
  return (
    <Router>
      {/* Fully Transparent Navbar */}
      <nav className="bg-transparent p-4 fixed w-full z-50">
        <div className="container mx-auto flex justify-between items-center">
          <div className="text-white text-xl font-bold drop-shadow-lg">
            <span className="text-orange-500">🏗️</span> TAIYLER Construction
          </div>
          <div className="space-x-6">
            <Link to="/" className="text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium">
              Home
            </Link>
            <Link to="/about" className="text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium">
              About
            </Link>
            <button className="bg-orange-600/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-orange-700/90 transition-all duration-300 drop-shadow-lg">
              Get Quote
            </button>
          </div>
        </div>
      </nav>

      {/* No padding needed as navbar is fully transparent */}
      <div>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
