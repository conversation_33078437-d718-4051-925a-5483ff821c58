@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Construction-themed Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-slide-up {
  animation: slideUp 1s ease-out;
}

.animate-slide-up-delay {
  animation: slideUp 1s ease-out 0.3s both;
}

.animate-fade-in-up {
  animation: slideInUp 1s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 1s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 1s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 1s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 1s ease-out;
}

.animate-bounce-subtle {
  animation: bounceSubtle 2s infinite;
}

.animate-pulse-slow {
  animation: pulseGlow 3s ease-in-out infinite;
}

.animate-count-up {
  animation: countUp 1.5s ease-out;
}

/* Hover Effects for Construction Theme */
.construction-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Responsive Animations */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-up-delay,
  .animate-fade-in-up,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-slide-in-up,
  .animate-slide-in-down,
  .animate-bounce-subtle,
  .animate-pulse-slow,
  .animate-count-up {
    animation: none;
  }
}

