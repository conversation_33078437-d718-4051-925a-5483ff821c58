{"ast": null, "code": "// App.js\nimport React from'react';import{BrowserRouter as Router,Route,Routes,Link}from'react-router-dom';import Home from'./components/Home';import About from'./components/About';import'./index.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsxs(Router,{children:[/*#__PURE__*/_jsx(\"nav\",{className:\"bg-transparent p-4 fixed w-full z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center space-x-3 group\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-white/10 rounded-full blur-sm group-hover:bg-white/20 transition-all duration-300\"}),/*#__PURE__*/_jsx(\"img\",{src:\"images/logo.png\",alt:\"Taiyler Construction Logo\",className:\"relative h-12 w-12 rounded-full border-2 border-white/20 group-hover:border-orange-400/60 transition-all duration-300 drop-shadow-xl\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xl font-bold drop-shadow-lg group-hover:text-orange-200 transition-colors duration-300\",children:\"TAIYLER\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-200 drop-shadow-lg -mt-1\",children:\"Construction\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-8\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium text-lg relative group\",children:[\"Home\",/*#__PURE__*/_jsx(\"span\",{className:\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-400 group-hover:w-full transition-all duration-300\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/about\",className:\"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium text-lg relative group\",children:[\"About\",/*#__PURE__*/_jsx(\"span\",{className:\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-400 group-hover:w-full transition-all duration-300\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-orange-600/80 backdrop-blur-sm text-white px-6 py-2.5 rounded-lg hover:bg-orange-700/90 hover:scale-105 transition-all duration-300 drop-shadow-lg font-semibold border border-orange-500/30\",children:\"Get Quote\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/about\",element:/*#__PURE__*/_jsx(About,{})})]})})]});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Link", "Home", "About", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "to", "src", "alt", "path", "element"], "sources": ["B:/Done projects/Taiyler/frontend/src/App.js"], "sourcesContent": ["// App.js\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport Home from './components/Home';\nimport About from './components/About';\nimport './index.css';\n\nfunction App() {\n  return (\n    <Router>\n      {/* Fully Transparent Navbar */}\n      <nav className=\"bg-transparent p-4 fixed w-full z-50\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          {/* Logo and Brand Section */}\n          <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 bg-white/10 rounded-full blur-sm group-hover:bg-white/20 transition-all duration-300\"></div>\n              <img\n                src=\"images/logo.png\"\n                alt=\"Taiyler Construction Logo\"\n                className=\"relative h-12 w-12 rounded-full border-2 border-white/20 group-hover:border-orange-400/60 transition-all duration-300 drop-shadow-xl\"\n              />\n            </div>\n            <div className=\"text-white\">\n              <div className=\"text-xl font-bold drop-shadow-lg group-hover:text-orange-200 transition-colors duration-300\">\n                TAIYLER\n              </div>\n              <div className=\"text-sm font-medium text-gray-200 drop-shadow-lg -mt-1\">\n                Construction\n              </div>\n            </div>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"flex items-center space-x-8\">\n            <Link to=\"/\" className=\"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium text-lg relative group\">\n              Home\n              <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-400 group-hover:w-full transition-all duration-300\"></span>\n            </Link>\n            <Link to=\"/about\" className=\"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium text-lg relative group\">\n              About\n              <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-400 group-hover:w-full transition-all duration-300\"></span>\n            </Link>\n            <button className=\"bg-orange-600/80 backdrop-blur-sm text-white px-6 py-2.5 rounded-lg hover:bg-orange-700/90 hover:scale-105 transition-all duration-300 drop-shadow-lg font-semibold border border-orange-500/30\">\n              Get Quote\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* No padding needed as navbar is fully transparent */}\n      <div>\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/about\" element={<About />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,KAAQ,kBAAkB,CAC/E,MAAO,CAAAC,IAAI,KAAM,mBAAmB,CACpC,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,KAAA,CAACT,MAAM,EAAAW,QAAA,eAELJ,IAAA,QAAKK,SAAS,CAAC,sCAAsC,CAAAD,QAAA,cACnDF,KAAA,QAAKG,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAElEF,KAAA,CAACN,IAAI,EAACU,EAAE,CAAC,GAAG,CAACD,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eACxDF,KAAA,QAAKG,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvBJ,IAAA,QAAKK,SAAS,CAAC,uGAAuG,CAAM,CAAC,cAC7HL,IAAA,QACEO,GAAG,CAAC,iBAAiB,CACrBC,GAAG,CAAC,2BAA2B,CAC/BH,SAAS,CAAC,sIAAsI,CACjJ,CAAC,EACC,CAAC,cACNH,KAAA,QAAKG,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBJ,IAAA,QAAKK,SAAS,CAAC,6FAA6F,CAAAD,QAAA,CAAC,SAE7G,CAAK,CAAC,cACNJ,IAAA,QAAKK,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAAC,cAExE,CAAK,CAAC,EACH,CAAC,EACF,CAAC,cAGPF,KAAA,QAAKG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CF,KAAA,CAACN,IAAI,EAACU,EAAE,CAAC,GAAG,CAACD,SAAS,CAAC,gHAAgH,CAAAD,QAAA,EAAC,MAEtI,cAAAJ,IAAA,SAAMK,SAAS,CAAC,kGAAkG,CAAO,CAAC,EACtH,CAAC,cACPH,KAAA,CAACN,IAAI,EAACU,EAAE,CAAC,QAAQ,CAACD,SAAS,CAAC,gHAAgH,CAAAD,QAAA,EAAC,OAE3I,cAAAJ,IAAA,SAAMK,SAAS,CAAC,kGAAkG,CAAO,CAAC,EACtH,CAAC,cACPL,IAAA,WAAQK,SAAS,CAAC,iMAAiM,CAAAD,QAAA,CAAC,WAEpN,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGNJ,IAAA,QAAAI,QAAA,cACEF,KAAA,CAACP,MAAM,EAAAS,QAAA,eACLJ,IAAA,CAACN,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEV,IAAA,CAACH,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCG,IAAA,CAACN,KAAK,EAACe,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEV,IAAA,CAACF,KAAK,GAAE,CAAE,CAAE,CAAC,EACrC,CAAC,CACN,CAAC,EACA,CAAC,CAEb,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}