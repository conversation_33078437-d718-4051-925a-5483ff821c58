{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Taiyler\\\\frontend\\\\src\\\\App.js\";\n// App.js\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport Home from './components/Home';\nimport About from './components/About';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"bg-transparent p-4 fixed w-full z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"\\\\images\\\\logo.png\",\n            alt: \"Taiyler Construction Logo\",\n            className: \"h- w-10 drop-shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-white text-xl font-bold drop-shadow-lg\",\n            children: \"TAIYLER Construction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-orange-600/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-orange-700/90 transition-all duration-300 drop-shadow-lg\",\n            children: \"Get Quote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/about\",\n          element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Link", "Home", "About", "jsxDEV", "_jsxDEV", "App", "children", "className", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "path", "element", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Taiyler/frontend/src/App.js"], "sourcesContent": ["// App.js\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport Home from './components/Home';\nimport About from './components/About';\nimport './index.css';\n\nfunction App() {\n  return (\n    <Router>\n      {/* Fully Transparent Navbar */}\n      <nav className=\"bg-transparent p-4 fixed w-full z-50\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-3\">\n            <img\n              src=\"\\images\\logo.png\"\n              alt=\"Taiyler Construction Logo\"\n              className=\"h- w-10 drop-shadow-lg\"\n            />\n            <div className=\"text-white text-xl font-bold drop-shadow-lg\">\n              TAIYLER Construction\n            </div>\n          </div>\n          <div className=\"space-x-6\">\n            <Link to=\"/\" className=\"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium\">\n              Home\n            </Link>\n            <Link to=\"/about\" className=\"text-white hover:text-orange-300 transition-all duration-300 drop-shadow-lg font-medium\">\n              About\n            </Link>\n            <button className=\"bg-orange-600/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-orange-700/90 transition-all duration-300 drop-shadow-lg\">\n              Get Quote\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* No padding needed as navbar is fully transparent */}\n      <div>\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/about\" element={<About />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,MAAM;IAAAS,QAAA,gBAELF,OAAA;MAAKG,SAAS,EAAC,sCAAsC;MAAAD,QAAA,eACnDF,OAAA;QAAKG,SAAS,EAAC,qDAAqD;QAAAD,QAAA,gBAClEF,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAD,QAAA,gBAC1CF,OAAA;YACEI,GAAG,EAAC,oBAAkB;YACtBC,GAAG,EAAC,2BAA2B;YAC/BF,SAAS,EAAC;UAAwB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFT,OAAA;YAAKG,SAAS,EAAC,6CAA6C;YAAAD,QAAA,EAAC;UAE7D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNT,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA,CAACJ,IAAI;YAACc,EAAE,EAAC,GAAG;YAACP,SAAS,EAAC,yFAAyF;YAAAD,QAAA,EAAC;UAEjH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA,CAACJ,IAAI;YAACc,EAAE,EAAC,QAAQ;YAACP,SAAS,EAAC,yFAAyF;YAAAD,QAAA,EAAC;UAEtH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA;YAAQG,SAAS,EAAC,qIAAqI;YAAAD,QAAA,EAAC;UAExJ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNT,OAAA;MAAAE,QAAA,eACEF,OAAA,CAACL,MAAM;QAAAO,QAAA,gBACLF,OAAA,CAACN,KAAK;UAACiB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEZ,OAAA,CAACH,IAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCT,OAAA,CAACN,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEZ,OAAA,CAACF,KAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACI,EAAA,GAvCQZ,GAAG;AAyCZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}